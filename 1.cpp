#include <bits/stdc++.h>
using namespace std;
struct transport{
    int car;
    int p;
    int t;
};
struct ticket{
    int p;
    int t;
};
int n;
int ans;
int main(){
    cin.tie(0)->sync_with_stdio(false);
    cin >> n;
    vector<transport> a(n+1);
    vector<ticket> tickets;
    for (int i=1;i<=n;i++){
        cin >> a[i].car >> a[i].p >> a[i].t;
    }
    for (int i=1;i<=n;i++){
        if (a[i].car == 0){
            ans += a[i].p;
            tickets.push_back({a[i].p, a[i].t});
        }
        else{
            for (int j = tickets.size()-1; j >= 0; j--){
                if (a[i].t - tickets[j].t > 45){
                    tickets.erase(tickets.begin() + j);
                }
            }
            int best = -1;
            for (int j = 0; j < tickets.size(); j++){
                if (tickets[j].p >= a[i].p){
                    if (best == -1 || tickets[j].t < tickets[best].t){
                        best = j;
                    }
                }
            }

            if (best != -1){
                tickets.erase(tickets.begin() + best);
            }
            else{
                ans += a[i].p;
            }
        }
    }
    cout << ans;
}